# LLM Configuration
# Choose LLM provider: openai, ollama, or azure
LLM_PROVIDER=ollama

# OpenAI Configuration (if using OpenAI)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
# Alternative models: gpt-4-turbo, gpt-4o, gpt-3.5-turbo, gpt-3.5-turbo-16k

# OpenAI-Compatible APIs (uncomment and modify as needed)
# For OpenRouter:
# OPENAI_BASE_URL=https://openrouter.ai/api/v1
# OPENAI_MODEL=anthropic/claude-3-sonnet

# For Together AI:
# OPENAI_BASE_URL=https://api.together.xyz/v1
# OPENAI_MODEL=meta-llama/Llama-2-70b-chat-hf

# For Groq:
# OPENAI_BASE_URL=https://api.groq.com/openai/v1
# OPENAI_MODEL=llama2-70b-4096

# For local OpenAI-compatible server (like vLLM, text-generation-webui):
# OPENAI_BASE_URL=http://localhost:8000/v1
# OPENAI_MODEL=your_local_model

# Ollama Configuration (if using Ollama)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
# Alternative models: codellama, mistral, neural-chat, etc.

# Azure OpenAI Configuration (if using Azure)
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2023-12-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name

# ClickHouse Configuration
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=demo_db

# Alternative: ClickHouse Cloud
# CLICKHOUSE_HOST=your-clickhouse-cloud-host.clickhouse.cloud
# CLICKHOUSE_PORT=8443
# CLICKHOUSE_USER=your_username
# CLICKHOUSE_PASSWORD=your_password
# CLICKHOUSE_SECURE=true

# Agent Configuration
MAX_CONSECUTIVE_AUTO_REPLY=10
TEMPERATURE=0.7
